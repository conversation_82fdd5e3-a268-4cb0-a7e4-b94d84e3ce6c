const { nanoid } = require('nanoid');
const fs = require('fs');
const path = require('path');
const { getLeadDeveloperCompletion } = require('./ai');
const { AICommentManager } = require('./ai-comments');

class AIAgentManager {
  constructor() {
    this.commentManager = new AICommentManager();
    this.agents = {
      productOwner: new ProductOwnerAgent(this.commentManager),
      scrumMaster: new ScrumMasterAgent(this.commentManager),
      leadDeveloper: new LeadDeveloperAgent(this.commentManager),
      developers: []
    };
  }

  async processUserStory(projectId, userStory) {
    console.log(`🤖 AI Agents processing user story: ${userStory.title}`);

    try {
      // 0. Log start of processing
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Starting AI processing of user story: ${userStory.title}`,
        'progress',
        { story_points: userStory.story_points, priority: userStory.priority }
      );

      // 1. Product Owner refines the story (reads comments first)
      const refinedStory = await this.agents.productOwner.refineStory(userStory, projectId);

      // 2. Scrum Master breaks down into tasks (reads comments first)
      const tasks = await this.agents.scrumMaster.breakdownStory(refinedStory, projectId);

      // 3. Lead Developer assigns and reviews (reads comments first)
      const assignedTasks = await this.agents.leadDeveloper.assignTasks(tasks, projectId, userStory.id);

      // 4. Generate initial code structure
      const codeStructure = await this.agents.leadDeveloper.generateCodeStructure(refinedStory, projectId);

      // 5. Create files in project workspace
      await this.createProjectFiles(projectId, codeStructure);

      // 6. Mark story as implemented
      await this.markStoryImplemented(userStory.id, projectId);

      // 7. Generate progress report
      const report = await this.agents.scrumMaster.generateProgressReport(projectId, userStory, tasks);
      await this.saveReport(projectId, report);

      // 8. Log completion
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Completed AI processing. Generated ${codeStructure.files?.length || 0} files and ${tasks.length} tasks.`,
        'progress',
        {
          files_generated: codeStructure.files?.length || 0,
          tasks_created: tasks.length,
          completion_status: 'success'
        }
      );

      return {
        refinedStory,
        tasks: assignedTasks,
        codeStructure,
        report
      };
    } catch (error) {
      console.error('Error processing user story:', error);

      // Log error as comment
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `ERROR during processing: ${error.message}`,
        'blocker',
        { error_type: error.name, stack_trace: error.stack },
        { priority: 'critical' }
      );

      throw error;
    }
  }

  async processSprintStories(projectId, sprintId) {
    console.log(`🚀 AI Agents starting sprint processing for sprint: ${sprintId}`);

    try {
      // Get all stories assigned to this sprint
      const stories = await this.getSprintStories(projectId, sprintId);
      const results = [];

      for (const story of stories) {
        if (story.implementation_status === 'not_started' || story.needs_reimplementation) {
          console.log(`📝 Processing story: ${story.title}`);
          const result = await this.processUserStory(projectId, story);
          results.push(result);
        }
      }

      // Generate sprint summary report
      const sprintReport = await this.agents.scrumMaster.generateSprintReport(projectId, sprintId, results);
      await this.saveReport(projectId, sprintReport, `sprint-${sprintId}`);

      return {
        processedStories: results.length,
        totalStories: stories.length,
        sprintReport
      };
    } catch (error) {
      console.error('Error processing sprint stories:', error);
      throw error;
    }
  }

  async createProjectFiles(projectId, codeStructure) {
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId, 'src');
    
    for (const file of codeStructure.files) {
      const filePath = path.join(workspacePath, file.path);
      const fileDir = path.dirname(filePath);
      
      // Create directory if it doesn't exist
      fs.mkdirSync(fileDir, { recursive: true });
      
      // Write file content
      fs.writeFileSync(filePath, file.content);
      console.log(`📁 Created file: ${file.path}`);
    }
  }

  async saveReport(projectId, report, prefix = 'report') {
    const reportsPath = path.join(__dirname, '..', 'workspaces', projectId, 'reports');
    const reportFile = path.join(reportsPath, `${prefix}-${Date.now()}.md`);

    fs.writeFileSync(reportFile, report);
    console.log(`📊 Generated report: ${reportFile}`);
  }

  async markStoryImplemented(storyId, projectId) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      const query = `UPDATE user_stories SET
        implementation_status = 'implemented',
        implemented_in_version = ?,
        needs_reimplementation = FALSE,
        updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND project_id = ?`;

      const version = `v${Date.now()}`; // Simple version numbering

      db.run(query, [version, storyId, projectId], function(err) {
        if (err) {
          reject(err);
        } else {
          console.log(`✅ Story ${storyId} marked as implemented in ${version}`);
          resolve(version);
        }
      });
    });
  }

  async getSprintStories(projectId, sprintId) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM user_stories WHERE project_id = ? AND sprint_id = ? ORDER BY priority DESC`;

      db.all(query, [projectId, sprintId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

class ProductOwnerAgent {
  constructor(commentManager) {
    this.commentManager = commentManager;
    this.agentName = 'ProductOwnerAgent';
  }

  async refineStory(userStory, projectId) {
    try {
      // Read existing comments first
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        userStory.id
      );

      // Check for blockers
      const blockers = await this.commentManager.getBlockers(
        projectId,
        'user_story',
        userStory.id
      );

      // Log start of refinement
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Starting story refinement process. Analyzing requirements and previous feedback.',
        'progress'
      );

      if (blockers.length > 0) {
        const blockerText = blockers.map(b => `- ${b.comment_text}`).join('\n');
        await this.commentManager.addComment(
          projectId,
          'user_story',
          userStory.id,
          this.agentName,
          `Found ${blockers.length} active blockers that need attention:\n${blockerText}`,
          'blocker',
          { blocker_count: blockers.length },
          { priority: 'high' }
        );
      }

      const prompt = `You are a Product Owner AI. Refine this user story and respond ONLY with valid JSON.

Story Details:
- Title: ${userStory.title}
- Description: ${userStory.description}
- Acceptance Criteria: ${userStory.acceptance_criteria}
- Priority: ${userStory.priority}
- Story Points: ${userStory.story_points}

Previous Comments and Context:
${commentsSummary}

${blockers.length > 0 ? `IMPORTANT - Active Blockers Found:
${blockers.map(b => `- ${b.comment_text}`).join('\n')}

Please address these blockers in your refinement.` : ''}

Return ONLY this JSON structure (no other text):
{
  "refined_acceptance_criteria": "More specific and testable criteria",
  "edge_cases": ["Edge case 1", "Edge case 2"],
  "dependencies": ["Dependency 1", "Dependency 2"],
  "definition_of_done": ["Done criteria 1", "Done criteria 2"]
}`;

      const response = await getLeadDeveloperCompletion(prompt);
      const cleanResponse = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
      const refinements = JSON.parse(cleanResponse);

      // Log successful refinement
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Story refinement completed',
        `Added ${refinements.edge_cases?.length || 0} edge cases, ${refinements.dependencies?.length || 0} dependencies, and ${refinements.definition_of_done?.length || 0} done criteria.`,
        {
          edge_cases_count: refinements.edge_cases?.length || 0,
          dependencies_count: refinements.dependencies?.length || 0,
          done_criteria_count: refinements.definition_of_done?.length || 0
        }
      );

      return {
        ...userStory,
        ...refinements,
        refined_by: 'ProductOwnerAgent',
        refined_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Product Owner refinement failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Refinement failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return userStory;
    }
  }
}

class ScrumMasterAgent {
  constructor(commentManager) {
    this.commentManager = commentManager;
    this.agentName = 'ScrumMasterAgent';
  }

  async breakdownStory(userStory, projectId) {
    try {
      // Read existing comments first
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        userStory.id
      );

      // Check for blockers
      const blockers = await this.commentManager.getBlockers(
        projectId,
        'user_story',
        userStory.id
      );

      // Log start of breakdown
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Starting task breakdown process. Analyzing story complexity and requirements.',
        'progress',
        { story_points: userStory.story_points }
      );

      const prompt = `You are a Scrum Master AI. Break down this user story into development tasks and respond ONLY with valid JSON.

Story Details:
- Title: ${userStory.title}
- Description: ${userStory.description}
- Acceptance Criteria: ${userStory.acceptance_criteria}
- Refined Acceptance Criteria: ${userStory.refined_acceptance_criteria || 'Not refined yet'}
- Edge Cases: ${JSON.stringify(userStory.edge_cases || [])}
- Dependencies: ${JSON.stringify(userStory.dependencies || [])}
- Definition of Done: ${JSON.stringify(userStory.definition_of_done || [])}
- Tech Stack: React + Node.js

Previous Comments and Context:
${commentsSummary}

${blockers.length > 0 ? `IMPORTANT - Active Blockers:
${blockers.map(b => `- ${b.comment_text}`).join('\n')}

Consider these blockers when creating tasks.` : ''}

Return ONLY this JSON structure (no other text):
{
  "tasks": [
    {
      "title": "Task title",
      "description": "Task description",
      "estimated_hours": 4,
      "priority": "high",
      "type": "frontend"
    }
  ]
}`;

      const response = await getLeadDeveloperCompletion(prompt);
      const cleanResponse = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
      const breakdown = JSON.parse(cleanResponse);

      const tasks = breakdown.tasks.map(task => ({
        id: nanoid(),
        ...task,
        story_id: userStory.id,
        status: 'todo',
        created_by: 'ScrumMasterAgent',
        created_at: new Date().toISOString()
      }));

      // Log successful breakdown with task details
      const totalHours = tasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Task breakdown completed',
        `Created ${tasks.length} tasks with total estimated effort of ${totalHours} hours. Tasks include: ${tasks.map(t => t.title).join(', ')}`,
        {
          task_count: tasks.length,
          total_estimated_hours: totalHours,
          task_types: [...new Set(tasks.map(t => t.type))]
        }
      );

      // Add comments for each task created
      for (const task of tasks) {
        await this.commentManager.addComment(
          projectId,
          'task',
          task.id,
          this.agentName,
          `Task created from user story: ${userStory.title}. Estimated effort: ${task.estimated_hours} hours.`,
          'progress',
          {
            parent_story_id: userStory.id,
            estimated_hours: task.estimated_hours,
            task_type: task.type,
            priority: task.priority
          }
        );
      }

      return tasks;
    } catch (error) {
      console.error('Scrum Master breakdown failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Task breakdown failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return [];
    }
  }

  async generateProgressReport(projectId, userStory, tasks) {
    const report = `# AI Development Progress Report

## Project: ${projectId}
## Generated: ${new Date().toISOString()}

### User Story Processed
**Title:** ${userStory.title}
**Priority:** ${userStory.priority}
**Story Points:** ${userStory.story_points}

### Tasks Generated
${tasks.map(task => `
- **${task.title}** (${task.type})
  - Estimated Hours: ${task.estimated_hours}
  - Priority: ${task.priority}
  - Status: ${task.status}
`).join('')}

### Next Steps
1. Review generated code structure
2. Run initial tests
3. Validate against acceptance criteria
4. Deploy to development environment

### AI Agents Involved
- Product Owner AI: Story refinement
- Scrum Master AI: Task breakdown and reporting
- Lead Developer AI: Code generation and architecture

---
*Report generated by Quarrel AI Development Team*
`;

    return report;
  }

  async generateSprintReport(projectId, sprintId, processedStories) {
    const report = `# Sprint Completion Report

## Project: ${projectId}
## Sprint: ${sprintId}
## Generated: ${new Date().toISOString()}

### Sprint Summary
- **Stories Processed**: ${processedStories.length}
- **Total Files Generated**: ${processedStories.reduce((sum, story) => sum + (story.codeStructure?.files?.length || 0), 0)}
- **Total Tasks Created**: ${processedStories.reduce((sum, story) => sum + (story.tasks?.length || 0), 0)}

### Stories Completed
${processedStories.map(story => `
#### ${story.refinedStory.title}
- **Priority**: ${story.refinedStory.priority}
- **Story Points**: ${story.refinedStory.story_points}
- **Files Generated**: ${story.codeStructure?.files?.length || 0}
- **Tasks Created**: ${story.tasks?.length || 0}
`).join('')}

### Development Artifacts
${processedStories.map(story =>
  story.codeStructure?.files?.map(file => `- ${file.path}: ${file.description}`).join('\n') || ''
).join('\n')}

### Next Sprint Recommendations
1. Review and test implemented features
2. Gather user feedback on completed stories
3. Plan next iteration based on sprint review
4. Address any technical debt identified

### AI Team Performance
- **Code Generation**: Successfully generated working code for all assigned stories
- **Task Breakdown**: Created detailed development tasks with time estimates
- **Documentation**: Generated comprehensive reports and documentation

---
*Sprint report generated by Quarrel AI Development Team*
`;

    return report;
  }
}

class LeadDeveloperAgent {
  constructor(commentManager) {
    this.commentManager = commentManager;
    this.agentName = 'LeadDeveloperAgent';
  }

  async assignTasks(tasks, projectId, storyId) {
    try {
      // Read comments for the user story to understand context
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        storyId
      );

      // Log start of assignment process
      await this.commentManager.addComment(
        projectId,
        'user_story',
        storyId,
        this.agentName,
        `Starting task assignment for ${tasks.length} tasks. Analyzing task complexity and dependencies.`,
        'progress',
        { task_count: tasks.length }
      );

      // For now, assign all tasks to AI developers
      const assignedTasks = tasks.map(task => ({
        ...task,
        assigned_to: 'AI-Developer',
        assigned_at: new Date().toISOString()
      }));

      // Log assignment decisions
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        storyId,
        this.agentName,
        'Task assignment completed',
        `Assigned ${assignedTasks.length} tasks to AI developers. Task distribution: ${assignedTasks.map(t => `${t.title} (${t.estimated_hours}h)`).join(', ')}`,
        {
          assigned_tasks: assignedTasks.length,
          total_hours: assignedTasks.reduce((sum, t) => sum + (t.estimated_hours || 0), 0)
        }
      );

      return assignedTasks;
    } catch (error) {
      console.error('Lead Developer assignment failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        storyId,
        this.agentName,
        `Task assignment failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return tasks;
    }
  }

  async generateCodeStructure(userStory, projectId) {
    try {
      // Get project details including tech stack
      const project = await this.getProjectDetails(projectId);
      const techStackInfo = this.getTechStackInfo(project.tech_stack);

      // Read comments for context
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        userStory.id
      );

      // Log start of code generation
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Starting code structure generation for ${techStackInfo.name}. Analyzing requirements and technical approach.`,
        'progress'
      );

      const prompt = `You are a Lead Developer AI. Generate ACTUAL WORKING CODE for this user story and respond ONLY with valid JSON.

Story Details:
- Title: ${userStory.title}
- Description: ${userStory.description}
- Refined Acceptance Criteria: ${userStory.refined_acceptance_criteria || 'Not refined yet'}
- Edge Cases: ${JSON.stringify(userStory.edge_cases || [])}
- Dependencies: ${JSON.stringify(userStory.dependencies || [])}
- Definition of Done: ${JSON.stringify(userStory.definition_of_done || [])}
- Tech Stack: ${techStackInfo.name}

IMPORTANT TECH STACK REQUIREMENTS:
${techStackInfo.requirements}

Previous Comments and Context:
${commentsSummary}

CRITICAL: Generate COMPLETE, FUNCTIONAL CODE - not comments or placeholders!

${techStackInfo.examples}

Return ONLY this JSON structure (no other text):
{
  "files": [
    {
      "path": "relative/path/to/file.ext",
      "content": "COMPLETE WORKING CODE HERE - NO COMMENTS OR PLACEHOLDERS",
      "description": "Brief description of what this file does"
    }
  ]
}`;

      const response = await getLeadDeveloperCompletion(prompt);
      const cleanResponse = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
      const structure = JSON.parse(cleanResponse);

      // Log successful code generation
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Code structure generation completed',
        `Generated ${structure.files?.length || 0} files: ${structure.files?.map(f => f.path).join(', ')}`,
        {
          files_generated: structure.files?.length || 0,
          file_paths: structure.files?.map(f => f.path) || []
        }
      );

      return {
        ...structure,
        generated_by: 'LeadDeveloperAgent',
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Lead Developer code generation failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Code generation failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return { files: [] };
    }
  }

  async getProjectDetails(projectId) {
    const db = require('./database');
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM projects WHERE id = ?', [projectId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  getTechStackInfo(techStack) {
    const techStacks = {
      'rust': {
        name: 'Rust + Actix/Warp',
        requirements: `
- Use Rust with either Actix-web or Warp framework
- Include proper Cargo.toml with all dependencies
- Use Serde for JSON serialization
- Include proper error handling with Result types
- Use async/await patterns
- Include database connectivity (SQLx or Diesel)
- Add CORS middleware for frontend integration
- Follow Rust naming conventions (snake_case)
- Include proper module structure`,
        examples: `
Example Cargo.toml:
[package]
name = "calendar-notes-api"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }
actix-cors = "0.6"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

Example main.rs structure:
use actix_web::{web, App, HttpServer, Result};
use actix_cors::Cors;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    HttpServer::new(|| {
        App::new()
            .wrap(Cors::permissive())
            .route("/health", web::get().to(health_check))
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}`
      },
      'react': {
        name: 'React + Node.js',
        requirements: `
- Use React with functional components and hooks
- Use Express.js for backend API
- Include proper package.json files
- Use modern JavaScript/TypeScript
- Include proper error handling
- Use RESTful API design
- Include CORS configuration`,
        examples: `
Example package.json:
{
  "name": "app",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}`
      }
    };

    return techStacks[techStack] || techStacks['react'];
  }
}

module.exports = { AIAgentManager };
