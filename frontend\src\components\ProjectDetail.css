/* Modern Dark Theme Layout */
.project-detail-modern {
  display: flex;
  height: 100vh;
  background: #0f0f0f;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #9ca3af;
}

.coming-soon h2 {
  margin: 0 0 16px 0;
  color: #d1d5db;
  font-size: 24px;
  font-weight: 600;
}

.coming-soon p {
  margin: 0;
  font-size: 16px;
}

/* Loading and Error States */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #1a1a1a;
  color: #e5e5e5;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #374151;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container h2 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  text-decoration: none;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* Responsive */
@media (max-width: 1200px) {
  .project-detail-modern {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
}

@media (max-width: 768px) {
  .project-detail-modern {
    flex-direction: column;
  }
}
