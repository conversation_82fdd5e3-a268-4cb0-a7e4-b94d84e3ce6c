require('dotenv').config();
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { nanoid } = require('nanoid');
const { spawn, exec } = require('child_process');
const db = require('./database');
const { getLeadDeveloperCompletion } = require('./ai');
const { AIAgentManager } = require('./ai-agents');
const { AIProcessingScheduler } = require('./ai-scheduler');

const app = express();
app.use(cors());
app.use(express.json());

// Initialize AI Agent Manager and Scheduler
const aiAgentManager = new AIAgentManager();
const aiScheduler = new AIProcessingScheduler();

// Track running processes for each project
const runningProcesses = new Map();

// Utility function to calculate sprint status based on dates
function calculateSprintStatus(startDate, endDate, currentDate = new Date()) {
    try {
        // Convert dates to Date objects for comparison
        const start = new Date(startDate);
        const end = new Date(endDate);
        const current = new Date(currentDate.toISOString().split('T')[0]); // Use date only, ignore time

        // Set time to start of day for accurate date comparison
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999); // End of day for end date
        current.setHours(0, 0, 0, 0);

        if (current < start) {
            return 'planning';
        } else if (current >= start && current <= end) {
            return 'active';
        } else {
            return 'completed';
        }
    } catch (error) {
        console.error('Error calculating sprint status:', error);
        return 'planning'; // Default fallback
    }
}

// Function to apply dynamic status to sprint objects
function applyDynamicSprintStatus(sprints) {
    return sprints.map(sprint => {
        // If sprint is manually cancelled, preserve that status
        if (sprint.status === 'cancelled') {
            return sprint;
        }

        // Calculate status based on dates
        const calculatedStatus = calculateSprintStatus(sprint.start_date, sprint.end_date);

        return {
            ...sprint,
            status: calculatedStatus
        };
    });
}

app.get('/', (req, res) => {
    res.send('Quarrel API Server - Project Management Platform');
});

// Projects endpoints
app.get('/projects', (req, res) => {
    db.all('SELECT * FROM projects ORDER BY created_at DESC', [], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ projects: rows });
    });
});

app.get('/api/projects', (req, res) => {
    db.all('SELECT * FROM projects ORDER BY created_at DESC', [], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ projects: rows });
    });
});

// Get individual project
app.get('/api/projects/:id', (req, res) => {
    const { id } = req.params;
    db.get('SELECT * FROM projects WHERE id = ?', [id], (err, row) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!row) {
            res.status(404).json({ error: 'Project not found' });
            return;
        }
        res.json(row);
    });
});

// Helper function to create project workspace
const createProjectWorkspace = (projectId, techStack) => {
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);

    try {
        // Create main project directory
        fs.mkdirSync(workspacePath, { recursive: true });

        // Create subdirectories
        fs.mkdirSync(path.join(workspacePath, 'src'), { recursive: true });
        fs.mkdirSync(path.join(workspacePath, 'tasks'), { recursive: true });
        fs.mkdirSync(path.join(workspacePath, 'reports'), { recursive: true });

        // Create project config file
        const config = {
            projectId,
            techStack,
            createdAt: new Date().toISOString(),
            structure: {
                src: 'Source code files',
                tasks: 'User stories and requirements',
                reports: 'AI-generated reports and progress updates'
            }
        };

        fs.writeFileSync(
            path.join(workspacePath, 'config.json'),
            JSON.stringify(config, null, 2)
        );

        // Create initial README
        const readme = `# Project Workspace\n\nThis workspace contains all files for the AI development team.\n\n## Structure\n- \`src/\` - Source code\n- \`tasks/\` - User stories\n- \`reports/\` - AI reports\n`;
        fs.writeFileSync(path.join(workspacePath, 'README.md'), readme);

        return workspacePath;
    } catch (error) {
        console.error('Error creating workspace:', error);
        throw error;
    }
};

app.post('/projects', (req, res) => {
    const {
        name,
        description,
        start_date,
        end_date,
        status = 'planning',
        tech_stack,
        ai_enabled = true,
        ai_config = {},
        repository_url
    } = req.body;

    if (!name || !tech_stack) {
        return res.status(400).json({ error: 'Project name and tech stack are required' });
    }

    const id = nanoid();

    try {
        // Create project workspace
        const workspacePath = createProjectWorkspace(id, tech_stack);

        const query = `INSERT INTO projects (
            id, name, description, start_date, end_date, status,
            tech_stack, workspace_path, ai_enabled, ai_config, repository_url, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;

        db.run(query, [
            id, name, description, start_date, end_date, status,
            tech_stack, workspacePath, ai_enabled, JSON.stringify(ai_config), repository_url
        ], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({
                id, name, description, start_date, end_date, status,
                tech_stack, workspace_path: workspacePath, ai_enabled, ai_config, repository_url
            });
        });
    } catch (error) {
        res.status(500).json({ error: 'Failed to create project workspace: ' + error.message });
    }
});

app.put('/projects/:id', (req, res) => {
    const { id } = req.params;
    const { name, description, start_date, end_date, status, progress } = req.body;

    const query = `UPDATE projects
                   SET name = ?, description = ?, start_date = ?, end_date = ?, status = ?, progress = ?, updated_at = CURRENT_TIMESTAMP
                   WHERE id = ?`;

    db.run(query, [name, description, start_date, end_date, status, progress, id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            return res.status(404).json({ error: 'Project not found' });
        }
        res.json({ id, name, description, start_date, end_date, status, progress });
    });
});

// Helper function to delete project workspace
const deleteProjectWorkspace = (projectId) => {
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);
    console.log(`Attempting to delete workspace: ${workspacePath}`);

    try {
        if (fs.existsSync(workspacePath)) {
            // Get directory stats before deletion for logging
            const stats = fs.statSync(workspacePath);
            fs.rmSync(workspacePath, { recursive: true, force: true });
            console.log(`✅ Successfully deleted workspace: ${workspacePath}`);
            console.log(`   Workspace was ${stats.isDirectory() ? 'directory' : 'file'} created on ${stats.birthtime}`);
        } else {
            console.log(`⚠️  Workspace directory not found: ${workspacePath}`);
        }
    } catch (error) {
        console.error(`❌ Error deleting workspace ${workspacePath}:`, error);
        // Don't throw - we still want the database deletion to succeed
    }
};

// Preview what will be deleted when deleting a project
app.get('/api/projects/:id/deletion-preview', (req, res) => {
    const { id } = req.params;

    // Get project details
    db.get('SELECT * FROM projects WHERE id = ?', [id], (err, project) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        // Count related data that will be deleted
        const queries = [
            { name: 'user_stories', query: 'SELECT COUNT(*) as count FROM user_stories WHERE project_id = ?' },
            { name: 'sprints', query: 'SELECT COUNT(*) as count FROM sprints WHERE project_id = ?' },
            { name: 'comments', query: 'SELECT COUNT(*) as count FROM comments WHERE project_id = ?' },
            { name: 'ai_queue_items', query: 'SELECT COUNT(*) as count FROM ai_processing_queue WHERE project_id = ?' }
        ];

        const counts = {};
        let completed = 0;

        queries.forEach(({ name, query }) => {
            db.get(query, [id], (err, result) => {
                if (!err) {
                    counts[name] = result.count;
                }
                completed++;

                if (completed === queries.length) {
                    // Check workspace
                    const workspacePath = path.join(__dirname, '..', 'workspaces', id);
                    const workspaceExists = fs.existsSync(workspacePath);

                    res.json({
                        project: {
                            id: project.id,
                            name: project.name,
                            workspace_path: project.workspace_path
                        },
                        will_delete: {
                            database: counts,
                            workspace: {
                                exists: workspaceExists,
                                path: workspacePath
                            }
                        }
                    });
                }
            });
        });
    });
});

app.delete('/projects/:id', (req, res) => {
    const { id } = req.params;

    // First get the project to check if it exists
    db.get('SELECT * FROM projects WHERE id = ?', [id], (err, project) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        // Delete from database (this will cascade to all related tables)
        db.run('DELETE FROM projects WHERE id = ?', [id], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            console.log(`✅ Successfully deleted project from database: ${id}`);

            // Delete workspace directory
            deleteProjectWorkspace(id);

            res.json({ message: 'Project deleted successfully' });
        });
    });
});

app.delete('/api/projects/:id', (req, res) => {
    const { id } = req.params;
    console.log(`Attempting to delete project: ${id}`);

    // First get the project to check if it exists
    db.get('SELECT * FROM projects WHERE id = ?', [id], (err, project) => {
        if (err) {
            console.error('Error finding project:', err.message);
            res.status(500).json({ error: err.message });
            return;
        }
        if (!project) {
            console.log(`Project not found: ${id}`);
            return res.status(404).json({ error: 'Project not found' });
        }

        console.log(`Found project: ${project.name}, proceeding with deletion`);
        console.log(`Project workspace path: ${project.workspace_path || 'Not set'}`);

        // Delete from database (this will cascade to all related tables)
        db.run('DELETE FROM projects WHERE id = ?', [id], function(err) {
            if (err) {
                console.error('Error deleting project from database:', err.message);
                res.status(500).json({ error: err.message });
                return;
            }

            console.log(`✅ Successfully deleted project from database: ${id} (${project.name})`);
            console.log(`   This will cascade delete all related user stories, sprints, comments, etc.`);

            // Delete workspace directory
            deleteProjectWorkspace(id);

            console.log(`🎉 Project deletion completed: ${project.name}`);
            res.json({ message: 'Project deleted successfully' });
        });
    });
});

app.get('/projects/:id/tasks', (req, res) => {
    const { id } = req.params;

    db.all('SELECT * FROM tasks WHERE project_id = ? ORDER BY created_at DESC', [id], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ tasks: rows });
    });
});

// Tasks endpoints (updated)
app.get('/tasks', (req, res) => {
    const { project_id } = req.query;
    let query = 'SELECT * FROM tasks';
    let params = [];

    if (project_id) {
        query += ' WHERE project_id = ?';
        params.push(project_id);
    }

    query += ' ORDER BY created_at DESC';

    db.all(query, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ tasks: rows });
    });
});

app.post('/tasks', (req, res) => {
    const { title, description, project_id, status = 'todo', priority = 'medium', assigned_to, due_date } = req.body;
    if (!title || !description) {
        return res.status(400).json({ error: 'Title and description are required' });
    }

    const id = nanoid();
    const query = `INSERT INTO tasks (id, title, description, project_id, status, priority, assigned_to, due_date, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;

    db.run(query, [id, title, description, project_id, status, priority, assigned_to, due_date], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({
            id,
            title,
            description,
            project_id,
            status,
            priority,
            assigned_to,
            due_date
        });
    });
});

app.post('/ai/task', async (req, res) => {
    const { prompt, project_id } = req.body;
    if (!prompt) {
        return res.status(400).json({ error: 'Prompt is required' });
    }

    try {
        const aiResponse = await getLeadDeveloperCompletion(
            `Based on the following prompt, create a task with a title and a description. Respond with a JSON object with "title" and "description" keys. Prompt: ${prompt}`
        );

        const { title, description } = JSON.parse(aiResponse);

        const query = `INSERT INTO tasks (title, description, project_id, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)`;

        db.run(query, [title, description, project_id], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: this.lastID, title, description, project_id });
        });
    } catch (error) {
        res.status(500).json({ error: 'Failed to get completion from AI' });
    }
});

// User Stories API endpoints
app.get('/projects/:id/stories', (req, res) => {
    const { id } = req.params;
    const query = `SELECT * FROM user_stories WHERE project_id = ? ORDER BY created_at DESC`;

    db.all(query, [id], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ stories: rows });
    });
});

app.get('/api/projects/:id/stories', (req, res) => {
    const { id } = req.params;
    const query = `SELECT * FROM user_stories WHERE project_id = ? ORDER BY created_at DESC`;

    db.all(query, [id], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ stories: rows });
    });
});

app.post('/projects/:id/stories', (req, res) => {
    const { id: project_id } = req.params;
    const {
        title,
        description,
        acceptance_criteria,
        priority = 'medium',
        story_points = 3,
        epic,
        user_persona = 'user',
        business_value,
        status = 'backlog'
    } = req.body;

    if (!title || !description || !acceptance_criteria) {
        return res.status(400).json({ error: 'Title, description, and acceptance criteria are required' });
    }

    const storyId = nanoid();
    const query = `INSERT INTO user_stories (
        id, project_id, title, description, acceptance_criteria,
        priority, story_points, epic, user_persona, business_value, status,
        implementation_status, last_modified_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;

    db.run(query, [
        storyId, project_id, title, description, acceptance_criteria,
        priority, story_points, epic, user_persona, business_value, status, 'not_started'
    ], async function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        const createdStory = {
            id: storyId,
            project_id,
            title,
            description,
            acceptance_criteria,
            priority,
            story_points,
            epic,
            user_persona,
            business_value,
            status
        };

        // Trigger AI processing in background
        setImmediate(async () => {
            try {
                console.log(`🚀 Starting AI processing for story: ${title}`);
                await aiAgentManager.processUserStory(project_id, createdStory);
                console.log(`✅ AI processing completed for story: ${title}`);
            } catch (error) {
                console.error(`❌ AI processing failed for story: ${title}`, error);
            }
        });

        res.json(createdStory);
    });
});

app.put('/projects/:projectId/stories/:storyId', (req, res) => {
    const { projectId, storyId } = req.params;
    const {
        title,
        description,
        acceptance_criteria,
        priority,
        story_points,
        epic,
        user_persona,
        business_value,
        status
    } = req.body;

    // First check if story was already implemented
    const checkQuery = `SELECT implementation_status, implemented_in_version FROM user_stories WHERE id = ? AND project_id = ?`;

    db.get(checkQuery, [storyId, projectId], (err, story) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!story) {
            res.status(404).json({ error: 'User story not found' });
            return;
        }

        // Determine if this story needs reimplementation
        const needsReimplementation = story.implementation_status === 'implemented';
        const newImplementationStatus = needsReimplementation ? 'modified_after_implementation' : story.implementation_status;

        const query = `UPDATE user_stories SET
            title = ?, description = ?, acceptance_criteria = ?, priority = ?,
            story_points = ?, epic = ?, user_persona = ?, business_value = ?,
            status = ?, implementation_status = ?, needs_reimplementation = ?,
            last_modified_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND project_id = ?`;

        db.run(query, [
            title, description, acceptance_criteria, priority,
            story_points, epic, user_persona, business_value, status,
            newImplementationStatus, needsReimplementation ? 1 : 0,
            storyId, projectId
        ], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            if (this.changes === 0) {
                res.status(404).json({ error: 'User story not found' });
                return;
            }
            res.json({
                message: 'User story updated successfully',
                needsReimplementation: needsReimplementation,
                implementationStatus: newImplementationStatus
            });
        });
    });
});

// PATCH endpoint for partial story updates (like sprint assignment)
app.patch('/projects/:projectId/stories/:storyId', (req, res) => {
    const { projectId, storyId } = req.params;
    const updates = req.body;

    // Build dynamic query based on provided fields
    const allowedFields = ['sprint_id', 'status', 'priority', 'story_points', 'title', 'description'];
    const updateFields = Object.keys(updates).filter(field => allowedFields.includes(field));

    if (updateFields.length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
    }

    const setClause = updateFields.map(field => `${field} = ?`).join(', ');
    const values = updateFields.map(field => updates[field]);
    values.push(storyId, projectId);

    const query = `UPDATE user_stories SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND project_id = ?`;

    db.run(query, values, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({
            message: 'User story updated successfully',
            changes: this.changes
        });
    });
});

app.patch('/api/projects/:projectId/stories/:storyId', (req, res) => {
    const { projectId, storyId } = req.params;
    const updates = req.body;

    // Build dynamic query based on provided fields
    const allowedFields = ['sprint_id', 'status', 'priority', 'story_points', 'title', 'description'];
    const updateFields = [];
    const values = [];

    Object.keys(updates).forEach(key => {
        if (allowedFields.includes(key)) {
            updateFields.push(`${key} = ?`);
            values.push(updates[key]);
        }
    });

    if (updateFields.length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
    }

    // Add timestamp and identifiers
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(storyId, projectId);

    const query = `UPDATE user_stories SET ${updateFields.join(', ')} WHERE id = ? AND project_id = ?`;

    db.run(query, values, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            return res.status(404).json({ error: 'Story not found' });
        }
        res.json({
            message: 'Story updated successfully',
            changes: this.changes
        });
    });
});

app.delete('/projects/:projectId/stories/:storyId', (req, res) => {
    const { projectId, storyId } = req.params;
    const query = `DELETE FROM user_stories WHERE id = ? AND project_id = ?`;

    db.run(query, [storyId, projectId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'User story not found' });
            return;
        }
        res.json({ message: 'User story deleted successfully' });
    });
});

app.delete('/api/projects/:projectId/stories/:storyId', (req, res) => {
    const { projectId, storyId } = req.params;
    const query = `DELETE FROM user_stories WHERE id = ? AND project_id = ?`;

    db.run(query, [storyId, projectId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'User story not found' });
            return;
        }
        res.json({ message: 'User story deleted successfully' });
    });
});

// AI Agent endpoints

// Manual trigger for AI processing of a specific user story (for testing)
app.post('/api/projects/:projectId/stories/:storyId/process', async (req, res) => {
    const { projectId, storyId } = req.params;

    try {
        // Get the user story
        const query = `SELECT * FROM user_stories WHERE id = ? AND project_id = ?`;

        db.get(query, [storyId, projectId], async (err, story) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            if (!story) {
                res.status(404).json({ error: 'User story not found' });
                return;
            }

            try {
                console.log(`🚀 Manual AI processing triggered for story: ${story.title}`);
                const result = await aiAgentManager.processUserStory(projectId, story);

                res.json({
                    message: 'AI processing completed successfully',
                    result: {
                        refinedStory: result.refinedStory,
                        tasksCreated: result.tasks?.length || 0,
                        filesGenerated: result.codeStructure?.files?.length || 0
                    }
                });
            } catch (error) {
                console.error('AI processing failed:', error);
                res.status(500).json({ error: 'AI processing failed: ' + error.message });
            }
        });
    } catch (error) {
        console.error('Error triggering AI processing:', error);
        res.status(500).json({ error: 'Failed to trigger AI processing: ' + error.message });
    }
});

app.post('/projects/:id/ai/process-sprint/:sprintId', async (req, res) => {
    const { id: projectId, sprintId } = req.params;

    try {
        console.log(`🚀 Manual sprint processing triggered for sprint: ${sprintId}`);
        const result = await aiAgentManager.processSprintStories(projectId, sprintId);
        res.json({
            message: 'Sprint processing completed successfully',
            result: {
                processedStories: result.processedStories,
                totalStories: result.totalStories,
                sprintReportGenerated: true
            }
        });
    } catch (error) {
        console.error('Sprint processing failed:', error);
        res.status(500).json({ error: 'Sprint processing failed: ' + error.message });
    }
});

app.post('/projects/:id/ai/process-story/:storyId', async (req, res) => {
    const { id: projectId, storyId } = req.params;

    try {
        // Get the user story
        const query = `SELECT * FROM user_stories WHERE id = ? AND project_id = ?`;
        db.get(query, [storyId, projectId], async (err, story) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            if (!story) {
                res.status(404).json({ error: 'User story not found' });
                return;
            }

            try {
                console.log(`🤖 Manual AI processing triggered for story: ${story.title}`);
                const result = await aiAgentManager.processUserStory(projectId, story);
                res.json({
                    message: 'AI processing completed successfully',
                    result: {
                        tasksGenerated: result.tasks.length,
                        filesCreated: result.codeStructure.files.length,
                        reportGenerated: true
                    }
                });
            } catch (error) {
                console.error('AI processing failed:', error);
                res.status(500).json({ error: 'AI processing failed: ' + error.message });
            }
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/projects/:id/ai/status', (req, res) => {
    const { id: projectId } = req.params;
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);

    try {
        const srcPath = path.join(workspacePath, 'src');
        const reportsPath = path.join(workspacePath, 'reports');

        const srcFiles = fs.existsSync(srcPath) ? fs.readdirSync(srcPath, { recursive: true }) : [];
        const reports = fs.existsSync(reportsPath) ? fs.readdirSync(reportsPath) : [];

        res.json({
            workspace: workspacePath,
            filesGenerated: srcFiles.length,
            reportsGenerated: reports.length,
            lastActivity: fs.existsSync(workspacePath) ? fs.statSync(workspacePath).mtime : null
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Comments API Endpoints

// Get comments for an entity (user story, task, sprint, or project)
app.get('/api/projects/:projectId/comments/:entityType/:entityId', (req, res) => {
    const { projectId, entityType, entityId } = req.params;
    const { includeInternal = 'false' } = req.query;

    let query = `SELECT c.*,
                        CASE WHEN c.parent_comment_id IS NOT NULL THEN
                            (SELECT comment_text FROM comments WHERE id = c.parent_comment_id)
                        ELSE NULL END as parent_comment_text
                 FROM comments c
                 WHERE c.project_id = ? AND c.entity_type = ? AND c.entity_id = ?`;

    const params = [projectId, entityType, entityId];

    if (includeInternal === 'false') {
        query += ' AND c.is_internal = FALSE';
    }

    query += ' ORDER BY c.created_at ASC';

    db.all(query, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // Parse metadata JSON for each comment
        const comments = rows.map(row => ({
            ...row,
            metadata: JSON.parse(row.metadata || '{}')
        }));

        res.json({ comments });
    });
});

// Create a new comment
app.post('/api/projects/:projectId/comments', (req, res) => {
    const { projectId } = req.params;
    const {
        entity_type,
        entity_id,
        author_type,
        author_name,
        comment_text,
        comment_type = 'progress',
        metadata = {},
        parent_comment_id = null,
        is_internal = false,
        priority = 'normal'
    } = req.body;

    if (!entity_type || !entity_id || !author_name || !comment_text) {
        return res.status(400).json({
            error: 'entity_type, entity_id, author_name, and comment_text are required'
        });
    }

    const commentId = nanoid();
    const query = `INSERT INTO comments (
        id, entity_type, entity_id, project_id, author_type, author_name,
        comment_text, comment_type, metadata, parent_comment_id, is_internal, priority
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    db.run(query, [
        commentId, entity_type, entity_id, projectId, author_type, author_name,
        comment_text, comment_type, JSON.stringify(metadata), parent_comment_id, is_internal, priority
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        const createdComment = {
            id: commentId,
            entity_type,
            entity_id,
            project_id: projectId,
            author_type,
            author_name,
            comment_text,
            comment_type,
            metadata,
            parent_comment_id,
            is_internal,
            priority,
            status: 'active',
            created_at: new Date().toISOString()
        };

        res.json(createdComment);
    });
});

// Update comment status (resolve, archive, etc.)
app.patch('/api/projects/:projectId/comments/:commentId', (req, res) => {
    const { projectId, commentId } = req.params;
    const { status, priority } = req.body;

    let query = 'UPDATE comments SET updated_at = CURRENT_TIMESTAMP';
    const params = [];

    if (status) {
        query += ', status = ?';
        params.push(status);
    }

    if (priority) {
        query += ', priority = ?';
        params.push(priority);
    }

    query += ' WHERE id = ? AND project_id = ?';
    params.push(commentId, projectId);

    db.run(query, params, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Comment not found' });
            return;
        }
        res.json({ message: 'Comment updated successfully' });
    });
});

// Get unread mentions for an AI agent
app.get('/api/projects/:projectId/mentions/:agentName', (req, res) => {
    const { projectId, agentName } = req.params;

    const query = `SELECT cm.*, c.comment_text, c.entity_type, c.entity_id, c.author_name
                   FROM comment_mentions cm
                   JOIN comments c ON cm.comment_id = c.id
                   WHERE c.project_id = ? AND cm.mentioned_agent = ? AND cm.is_read = FALSE
                   ORDER BY cm.created_at DESC`;

    db.all(query, [projectId, agentName], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ mentions: rows });
    });
});

// Mark mentions as read
app.patch('/api/projects/:projectId/mentions/:agentName/read', (req, res) => {
    const { projectId, agentName } = req.params;
    const { mentionIds } = req.body;

    if (!mentionIds || !Array.isArray(mentionIds)) {
        return res.status(400).json({ error: 'mentionIds array is required' });
    }

    const placeholders = mentionIds.map(() => '?').join(',');
    const query = `UPDATE comment_mentions
                   SET is_read = TRUE
                   WHERE id IN (${placeholders})
                   AND mentioned_agent = ?`;

    db.run(query, [...mentionIds, agentName], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: `Marked ${this.changes} mentions as read` });
    });
});

// AI Scheduler Management Endpoints

// Get AI scheduler status
app.get('/api/ai/scheduler/status', (req, res) => {
    const status = aiScheduler.getStatus();
    res.json(status);
});

// Start AI scheduler
app.post('/api/ai/scheduler/start', async (req, res) => {
    const { config } = req.body;

    try {
        const started = await aiScheduler.start(config);

        if (started) {
            // Save state to database
            const query = `INSERT OR REPLACE INTO ai_scheduler_state (
                id, is_running, tick_rate, config, started_at, updated_at
            ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;

            db.run(query, [
                'singleton',
                true,
                aiScheduler.config.tickRate,
                JSON.stringify(aiScheduler.config)
            ]);

            res.json({
                message: 'AI Scheduler started successfully',
                status: aiScheduler.getStatus()
            });
        } else {
            res.status(400).json({ error: 'AI Scheduler is already running' });
        }
    } catch (error) {
        console.error('Error starting AI scheduler:', error);
        res.status(500).json({ error: 'Failed to start AI scheduler: ' + error.message });
    }
});

// Stop AI scheduler
app.post('/api/ai/scheduler/stop', async (req, res) => {
    try {
        const stopped = await aiScheduler.stop();

        if (stopped) {
            // Update state in database
            const query = `UPDATE ai_scheduler_state SET
                is_running = FALSE,
                stopped_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP,
                stats = ?
                WHERE id = 'singleton'`;

            db.run(query, [JSON.stringify(aiScheduler.stats)]);

            res.json({
                message: 'AI Scheduler stopped successfully',
                finalStats: aiScheduler.stats
            });
        } else {
            res.status(400).json({ error: 'AI Scheduler is not running' });
        }
    } catch (error) {
        console.error('Error stopping AI scheduler:', error);
        res.status(500).json({ error: 'Failed to stop AI scheduler: ' + error.message });
    }
});

// Update AI scheduler configuration
app.patch('/api/ai/scheduler/config', (req, res) => {
    const { config } = req.body;

    if (!config) {
        return res.status(400).json({ error: 'Configuration is required' });
    }

    try {
        aiScheduler.updateConfig(config);

        // Update config in database
        const query = `UPDATE ai_scheduler_state SET
            config = ?,
            tick_rate = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = 'singleton'`;

        db.run(query, [
            JSON.stringify(aiScheduler.config),
            aiScheduler.config.tickRate
        ]);

        res.json({
            message: 'AI Scheduler configuration updated',
            config: aiScheduler.config
        });
    } catch (error) {
        console.error('Error updating AI scheduler config:', error);
        res.status(500).json({ error: 'Failed to update configuration: ' + error.message });
    }
});

// Update project AI processing status
app.patch('/api/projects/:projectId/ai-processing', (req, res) => {
    const { projectId } = req.params;
    const { ai_processing_enabled, ai_processing_status } = req.body;

    let query = 'UPDATE projects SET updated_at = CURRENT_TIMESTAMP';
    const params = [];

    if (ai_processing_enabled !== undefined) {
        query += ', ai_processing_enabled = ?';
        params.push(ai_processing_enabled);
    }

    if (ai_processing_status) {
        query += ', ai_processing_status = ?';
        params.push(ai_processing_status);
    }

    query += ' WHERE id = ?';
    params.push(projectId);

    db.run(query, params, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Project not found' });
            return;
        }
        res.json({ message: 'Project AI processing status updated' });
    });
});

// Get active sprints across all projects
app.get('/api/sprints/active', (req, res) => {
    const now = new Date().toISOString().split('T')[0];

    // Get all sprints that fall within the current date range (excluding cancelled ones)
    const query = `SELECT s.*, p.name as project_name
                   FROM sprints s
                   JOIN projects p ON s.project_id = p.id
                   WHERE s.status != 'cancelled'
                   AND s.start_date <= ?
                   AND s.end_date >= ?
                   ORDER BY s.start_date ASC`;

    db.all(query, [now, now], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // Apply dynamic status calculation and filter for active ones
        const sprintsWithDynamicStatus = applyDynamicSprintStatus(rows);
        const activeSprints = sprintsWithDynamicStatus.filter(sprint => sprint.status === 'active');

        res.json({ sprints: activeSprints });
    });
});

// Update sprint AI processing status
app.patch('/api/projects/:projectId/sprints/:sprintId/ai-processing', (req, res) => {
    const { projectId, sprintId } = req.params;
    const { ai_processing_enabled } = req.body;

    const query = `UPDATE sprints SET
                   ai_processing_enabled = ?,
                   updated_at = CURRENT_TIMESTAMP
                   WHERE id = ? AND project_id = ?`;

    db.run(query, [ai_processing_enabled, sprintId, projectId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Sprint not found' });
            return;
        }
        res.json({ message: 'Sprint AI processing status updated' });
    });
});

// Reports API Endpoints

// Get all reports across all projects (for dashboard)
app.get('/api/reports', (req, res) => {
    try {
        const workspacesPath = path.join(__dirname, '..', 'workspaces');
        const allReports = [];

        if (!fs.existsSync(workspacesPath)) {
            return res.json({ reports: [] });
        }

        const projectDirs = fs.readdirSync(workspacesPath);

        for (const projectId of projectDirs) {
            const projectPath = path.join(workspacesPath, projectId);
            const reportsPath = path.join(projectPath, 'reports');

            if (fs.existsSync(reportsPath)) {
                const reportFiles = fs.readdirSync(reportsPath);

                for (const reportFile of reportFiles) {
                    if (reportFile.endsWith('.md')) {
                        try {
                            const reportPath = path.join(reportsPath, reportFile);
                            const content = fs.readFileSync(reportPath, 'utf8');
                            const stats = fs.statSync(reportPath);

                            // Parse report metadata from filename and content
                            const report = parseReportContent(content, reportFile, projectId, stats);
                            allReports.push(report);
                        } catch (err) {
                            console.error(`Error reading report ${reportFile}:`, err);
                        }
                    }
                }
            }
        }

        // Sort by date (newest first)
        allReports.sort((a, b) => new Date(b.generated_at) - new Date(a.generated_at));

        res.json({ reports: allReports });
    } catch (error) {
        console.error('Error fetching all reports:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get reports for a specific project
app.get('/api/projects/:id/reports', (req, res) => {
    const { id: projectId } = req.params;
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);
    const reportsPath = path.join(workspacePath, 'reports');

    try {
        const reports = [];

        if (fs.existsSync(reportsPath)) {
            const reportFiles = fs.readdirSync(reportsPath);

            for (const reportFile of reportFiles) {
                if (reportFile.endsWith('.md')) {
                    try {
                        const reportPath = path.join(reportsPath, reportFile);
                        const content = fs.readFileSync(reportPath, 'utf8');
                        const stats = fs.statSync(reportPath);

                        const report = parseReportContent(content, reportFile, projectId, stats);
                        reports.push(report);
                    } catch (err) {
                        console.error(`Error reading report ${reportFile}:`, err);
                    }
                }
            }
        }

        // Sort by date (newest first)
        reports.sort((a, b) => new Date(b.generated_at) - new Date(a.generated_at));

        res.json({ reports });
    } catch (error) {
        console.error('Error fetching project reports:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get a specific report
app.get('/api/projects/:id/reports/:reportId', (req, res) => {
    const { id: projectId, reportId } = req.params;
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);
    const reportsPath = path.join(workspacePath, 'reports');

    try {
        const reportFiles = fs.readdirSync(reportsPath);
        const reportFile = reportFiles.find(file => file.includes(reportId));

        if (!reportFile) {
            return res.status(404).json({ error: 'Report not found' });
        }

        const reportPath = path.join(reportsPath, reportFile);
        const content = fs.readFileSync(reportPath, 'utf8');
        const stats = fs.statSync(reportPath);

        const report = parseReportContent(content, reportFile, projectId, stats);
        res.json(report);
    } catch (error) {
        console.error('Error fetching specific report:', error);
        res.status(500).json({ error: error.message });
    }
});

// Helper function to parse report content and extract metadata
function parseReportContent(content, filename, projectId, stats) {
    const lines = content.split('\n');
    let title = 'Untitled Report';
    let type = 'progress';
    let generatedAt = stats.mtime;
    let summary = '';

    // Extract title from first heading
    const titleLine = lines.find(line => line.startsWith('# '));
    if (titleLine) {
        title = titleLine.replace('# ', '').trim();
    }

    // Determine report type from filename or content
    if (filename.includes('sprint-')) {
        type = 'sprint';
    } else if (filename.includes('analytics-')) {
        type = 'analytics';
    }

    // Extract generated date from content if available
    const generatedLine = lines.find(line => line.includes('Generated:'));
    if (generatedLine) {
        const dateMatch = generatedLine.match(/Generated:\s*(.+)/);
        if (dateMatch) {
            try {
                generatedAt = new Date(dateMatch[1]);
            } catch (e) {
                // Keep file modification time as fallback
            }
        }
    }

    // Extract summary from content (first paragraph after title)
    const contentStart = lines.findIndex(line => line.startsWith('# ')) + 1;
    const summaryLines = [];
    for (let i = contentStart; i < lines.length && summaryLines.length < 3; i++) {
        const line = lines[i].trim();
        if (line && !line.startsWith('#') && !line.startsWith('##')) {
            summaryLines.push(line);
        }
    }
    summary = summaryLines.join(' ').substring(0, 200) + '...';

    // Convert markdown to HTML (basic conversion)
    const htmlContent = convertMarkdownToHtml(content);

    return {
        id: filename.replace('.md', ''),
        title,
        type,
        content,
        html_content: htmlContent,
        summary,
        generated_at: generatedAt.toISOString(),
        project_id: projectId,
        file_size: stats.size
    };
}

// Basic markdown to HTML converter
function convertMarkdownToHtml(markdown) {
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>');

    // Inline code
    html = html.replace(/`([^`]*)`/gim, '<code>$1</code>');

    // Lists
    html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>');

    // Line breaks
    html = html.replace(/\n/gim, '<br>');

    return html;
}

// Delete a specific report
app.delete('/api/reports/:reportId', (req, res) => {
    const { reportId } = req.params;

    try {
        // Find the report file across all project workspaces
        const workspacesPath = path.join(__dirname, '..', 'workspaces');
        let reportDeleted = false;

        if (fs.existsSync(workspacesPath)) {
            const projectDirs = fs.readdirSync(workspacesPath);

            for (const projectDir of projectDirs) {
                const reportsPath = path.join(workspacesPath, projectDir, 'reports');

                if (fs.existsSync(reportsPath)) {
                    const reportFiles = fs.readdirSync(reportsPath);

                    for (const reportFile of reportFiles) {
                        if (reportFile.endsWith('.md')) {
                            const reportPath = path.join(reportsPath, reportFile);
                            const content = fs.readFileSync(reportPath, 'utf8');
                            const stats = fs.statSync(reportPath);

                            const report = parseReportContent(content, reportFile, projectDir, stats);

                            if (report.id === reportId) {
                                fs.unlinkSync(reportPath);
                                reportDeleted = true;
                                break;
                            }
                        }
                    }

                    if (reportDeleted) break;
                }
            }
        }

        if (reportDeleted) {
            res.json({ message: 'Report deleted successfully' });
        } else {
            res.status(404).json({ error: 'Report not found' });
        }
    } catch (error) {
        console.error('Error deleting report:', error);
        res.status(500).json({ error: error.message });
    }
});

// Delete a specific report from a project
app.delete('/api/projects/:id/reports/:reportId', (req, res) => {
    const { id: projectId, reportId } = req.params;
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);
    const reportsPath = path.join(workspacePath, 'reports');

    try {
        let reportDeleted = false;

        if (fs.existsSync(reportsPath)) {
            const reportFiles = fs.readdirSync(reportsPath);

            for (const reportFile of reportFiles) {
                if (reportFile.endsWith('.md')) {
                    const reportPath = path.join(reportsPath, reportFile);
                    const content = fs.readFileSync(reportPath, 'utf8');
                    const stats = fs.statSync(reportPath);

                    const report = parseReportContent(content, reportFile, projectId, stats);

                    if (report.id === reportId) {
                        fs.unlinkSync(reportPath);
                        reportDeleted = true;
                        break;
                    }
                }
            }
        }

        if (reportDeleted) {
            res.json({ message: 'Report deleted successfully' });
        } else {
            res.status(404).json({ error: 'Report not found' });
        }
    } catch (error) {
        console.error('Error deleting report:', error);
        res.status(500).json({ error: error.message });
    }
});

// Sprint Management Endpoints

// Get all sprints for a project
app.get('/projects/:id/sprints', (req, res) => {
    const { id: projectId } = req.params;
    const query = `SELECT * FROM sprints WHERE project_id = ? ORDER BY created_at DESC`;

    db.all(query, [projectId], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // Apply dynamic status calculation based on dates
        const sprintsWithDynamicStatus = applyDynamicSprintStatus(rows);
        res.json({ sprints: sprintsWithDynamicStatus });
    });
});

// Create a new sprint
app.post('/projects/:id/sprints', (req, res) => {
    const { id: projectId } = req.params;
    const {
        name,
        description,
        start_date,
        end_date,
        goals,
        capacity_hours = 40
    } = req.body;

    if (!name || !start_date || !end_date) {
        return res.status(400).json({ error: 'Name, start date, and end date are required' });
    }

    const sprintId = nanoid();
    const query = `INSERT INTO sprints (
        id, project_id, name, description, start_date, end_date, goals, capacity_hours, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;

    db.run(query, [sprintId, projectId, name, description, start_date, end_date, goals, capacity_hours], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({
            id: sprintId,
            project_id: projectId,
            name,
            description,
            start_date,
            end_date,
            goals,
            capacity_hours,
            status: 'planning'
        });
    });
});

// Update sprint status
app.patch('/projects/:projectId/sprints/:sprintId', (req, res) => {
    const { projectId, sprintId } = req.params;
    const { status, name, description, goals, start_date, end_date, capacity_hours } = req.body;

    const updates = [];
    const values = [];

    if (status) {
        updates.push('status = ?');
        values.push(status);
    }
    if (name) {
        updates.push('name = ?');
        values.push(name);
    }
    if (description) {
        updates.push('description = ?');
        values.push(description);
    }
    if (goals) {
        updates.push('goals = ?');
        values.push(goals);
    }
    if (start_date) {
        updates.push('start_date = ?');
        values.push(start_date);
    }
    if (end_date) {
        updates.push('end_date = ?');
        values.push(end_date);
    }
    if (capacity_hours) {
        updates.push('capacity_hours = ?');
        values.push(capacity_hours);
    }

    if (updates.length === 0) {
        return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(sprintId, projectId);

    const query = `UPDATE sprints SET ${updates.join(', ')} WHERE id = ? AND project_id = ?`;

    db.run(query, values, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Sprint not found' });
            return;
        }
        res.json({ message: 'Sprint updated successfully' });
    });
});

// Assign user stories to sprint
app.post('/projects/:projectId/sprints/:sprintId/stories', (req, res) => {
    const { projectId, sprintId } = req.params;
    const { storyIds } = req.body;

    if (!Array.isArray(storyIds) || storyIds.length === 0) {
        return res.status(400).json({ error: 'Story IDs array is required' });
    }

    const placeholders = storyIds.map(() => '?').join(',');
    const query = `UPDATE user_stories SET sprint_id = ?, updated_at = CURRENT_TIMESTAMP
                   WHERE id IN (${placeholders}) AND project_id = ?`;

    db.run(query, [sprintId, ...storyIds, projectId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({
            message: `${this.changes} stories assigned to sprint`,
            assignedCount: this.changes
        });
    });
});

// Remove user stories from sprint
app.delete('/projects/:projectId/sprints/:sprintId/stories', (req, res) => {
    const { projectId, sprintId } = req.params;
    const { storyIds } = req.body;

    if (!Array.isArray(storyIds) || storyIds.length === 0) {
        return res.status(400).json({ error: 'Story IDs array is required' });
    }

    const placeholders = storyIds.map(() => '?').join(',');
    const query = `UPDATE user_stories SET sprint_id = NULL, updated_at = CURRENT_TIMESTAMP
                   WHERE id IN (${placeholders}) AND project_id = ? AND sprint_id = ?`;

    db.run(query, [...storyIds, projectId, sprintId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({
            message: `${this.changes} stories removed from sprint`,
            removedCount: this.changes
        });
    });
});

// Get sprint stories
app.get('/projects/:projectId/sprints/:sprintId/stories', (req, res) => {
    const { projectId, sprintId } = req.params;
    const query = `SELECT * FROM user_stories WHERE project_id = ? AND sprint_id = ? ORDER BY priority DESC, created_at ASC`;

    db.all(query, [projectId, sprintId], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ stories: rows });
    });
});

// Create sprint review
app.post('/projects/:projectId/sprints/:sprintId/review', (req, res) => {
    const { projectId, sprintId } = req.params;
    const {
        user_feedback,
        ai_performance_rating,
        review_notes
    } = req.body;

    // Get sprint statistics
    const statsQuery = `
        SELECT
            COUNT(*) as total_stories,
            SUM(CASE WHEN implementation_status = 'implemented' THEN 1 ELSE 0 END) as completed_stories
        FROM user_stories
        WHERE project_id = ? AND sprint_id = ?`;

    db.get(statsQuery, [projectId, sprintId], (err, stats) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        const reviewId = nanoid();
        const insertQuery = `INSERT INTO sprint_reviews (
            id, sprint_id, project_id, user_feedback, ai_performance_rating,
            completed_stories, total_stories, review_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

        db.run(insertQuery, [
            reviewId, sprintId, projectId, user_feedback, ai_performance_rating,
            stats.completed_stories, stats.total_stories, review_notes
        ], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({
                id: reviewId,
                sprint_id: sprintId,
                project_id: projectId,
                user_feedback,
                ai_performance_rating,
                completed_stories: stats.completed_stories,
                total_stories: stats.total_stories,
                review_notes
            });
        });
    });
});

// Build and Run Endpoints

// Helper function to get tech stack build commands
function getBuildCommands(techStack) {
    const commands = {
        'rust': {
            build: 'cargo build',
            run: 'cargo run',
            check: 'cargo check',
            workingDir: 'src',
            defaultPort: 8080,
            urlPath: ''
        },
        'react': {
            build: 'npm install && npm run build',
            run: 'npm start',
            check: 'npm run lint',
            workingDir: '.',
            defaultPort: 3000,
            urlPath: ''
        },
        'python': {
            build: 'pip install -r requirements.txt',
            run: 'python main.py',
            check: 'python -m py_compile *.py',
            workingDir: '.',
            defaultPort: 5000,
            urlPath: ''
        },
        'go': {
            build: 'go build',
            run: 'go run .',
            check: 'go vet',
            workingDir: '.',
            defaultPort: 8080,
            urlPath: ''
        }
    };

    return commands[techStack] || commands['react'];
}

// Helper function to detect the actual port from application output
function detectApplicationPort(output, defaultPort) {
    // Common patterns for port detection
    const portPatterns = [
        /listening on (?:.*:)?(\d+)/i,
        /server (?:running|started) (?:on|at) (?:.*:)?(\d+)/i,
        /port (\d+)/i,
        /localhost:(\d+)/i,
        /127\.0\.0\.1:(\d+)/i,
        /0\.0\.0\.0:(\d+)/i
    ];

    for (const pattern of portPatterns) {
        const match = output.match(pattern);
        if (match && match[1]) {
            return parseInt(match[1]);
        }
    }

    return defaultPort;
}

// AI-powered build fix function
async function attemptBuildFix(projectId, techStack, buildError, projectSrcPath) {
    try {
        // Analyze the error and determine fix strategy
        const errorAnalysis = analyzeBuildError(buildError, techStack);

        if (!errorAnalysis.fixable) {
            return {
                fixed: false,
                message: `❌ Unable to auto-fix: ${errorAnalysis.reason}`
            };
        }

        // Apply the fix based on error type
        const fixApplied = await applyBuildFix(errorAnalysis, projectSrcPath, techStack);

        if (fixApplied) {
            return {
                fixed: true,
                message: `✅ Applied fix: ${errorAnalysis.fixDescription}`
            };
        } else {
            return {
                fixed: false,
                message: `❌ Failed to apply fix: ${errorAnalysis.fixDescription}`
            };
        }
    } catch (error) {
        return {
            fixed: false,
            message: `❌ Fix attempt error: ${error.message}`
        };
    }
}

// Analyze build errors to determine fix strategy
function analyzeBuildError(buildError, techStack) {
    const errorText = buildError.toLowerCase();

    if (techStack === 'rust') {
        // Missing Cargo.toml package section
        if (errorText.includes('manifest is missing either a `[package]` or a `[workspace]`')) {
            return {
                fixable: true,
                type: 'missing_cargo_package',
                fixDescription: 'Adding missing [package] section to Cargo.toml',
                reason: null
            };
        }

        // Missing dependencies
        if (errorText.includes('unresolved import') || errorText.includes('cannot find')) {
            return {
                fixable: true,
                type: 'missing_dependencies',
                fixDescription: 'Adding missing dependencies to Cargo.toml',
                reason: null
            };
        }

        // Syntax errors in Cargo.toml
        if (errorText.includes('failed to parse manifest') && errorText.includes('cargo.toml')) {
            return {
                fixable: true,
                type: 'cargo_syntax_error',
                fixDescription: 'Fixing Cargo.toml syntax errors',
                reason: null
            };
        }
    }

    if (techStack === 'react') {
        // Missing package.json
        if (errorText.includes('package.json') && errorText.includes('not found')) {
            return {
                fixable: true,
                type: 'missing_package_json',
                fixDescription: 'Creating missing package.json',
                reason: null
            };
        }

        // Missing dependencies
        if (errorText.includes('module not found') || errorText.includes('cannot resolve')) {
            return {
                fixable: true,
                type: 'missing_npm_dependencies',
                fixDescription: 'Installing missing npm dependencies',
                reason: null
            };
        }
    }

    return {
        fixable: false,
        type: 'unknown',
        fixDescription: null,
        reason: 'Unknown error type - manual intervention required'
    };
}

// Apply the determined fix
async function applyBuildFix(errorAnalysis, projectSrcPath, techStack) {
    try {
        switch (errorAnalysis.type) {
            case 'missing_cargo_package':
                return await fixMissingCargoPackage(projectSrcPath);

            case 'missing_dependencies':
                return await fixMissingDependencies(projectSrcPath, techStack);

            case 'cargo_syntax_error':
                return await fixCargoSyntaxError(projectSrcPath);

            case 'missing_package_json':
                return await fixMissingPackageJson(projectSrcPath);

            case 'missing_npm_dependencies':
                return await fixMissingNpmDependencies(projectSrcPath);

            default:
                return false;
        }
    } catch (error) {
        console.error('Error applying build fix:', error);
        return false;
    }
}

// Fix missing Cargo.toml [package] section
async function fixMissingCargoPackage(projectSrcPath) {
    try {
        const cargoTomlPath = path.join(projectSrcPath, 'Cargo.toml');

        // Check if Cargo.toml exists
        if (!fs.existsSync(cargoTomlPath)) {
            // Create a complete Cargo.toml
            const cargoContent = `[package]
name = "actix-web-api"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
actix-cors = "0.6"
`;
            fs.writeFileSync(cargoTomlPath, cargoContent);
            return true;
        }

        // Read existing Cargo.toml
        let cargoContent = fs.readFileSync(cargoTomlPath, 'utf8');

        // Check if [package] section is missing
        if (!cargoContent.includes('[package]')) {
            // Replace with complete valid Cargo.toml
            const completeCargoContent = `[package]
name = "actix-web-api"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
actix-cors = "0.6"

# Original comments preserved below:
${cargoContent}
`;
            fs.writeFileSync(cargoTomlPath, completeCargoContent);
            return true;
        }

        return false;
    } catch (error) {
        console.error('Error fixing Cargo.toml package section:', error);
        return false;
    }
}

// Fix missing dependencies
async function fixMissingDependencies(projectSrcPath, techStack) {
    try {
        if (techStack === 'rust') {
            const cargoTomlPath = path.join(projectSrcPath, 'Cargo.toml');

            if (fs.existsSync(cargoTomlPath)) {
                let cargoContent = fs.readFileSync(cargoTomlPath, 'utf8');

                // Add common dependencies if missing
                if (!cargoContent.includes('[dependencies]')) {
                    cargoContent += `
[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
actix-cors = "0.6"
`;
                    fs.writeFileSync(cargoTomlPath, cargoContent);
                    return true;
                }
            }
        }

        return false;
    } catch (error) {
        console.error('Error fixing missing dependencies:', error);
        return false;
    }
}

// Fix Cargo.toml syntax errors
async function fixCargoSyntaxError(projectSrcPath) {
    try {
        const cargoTomlPath = path.join(projectSrcPath, 'Cargo.toml');

        if (fs.existsSync(cargoTomlPath)) {
            let cargoContent = fs.readFileSync(cargoTomlPath, 'utf8');

            // Check if file is mostly comments or empty
            const lines = cargoContent.split('\n');
            const nonCommentLines = lines.filter(line =>
                line.trim() && !line.trim().startsWith('#')
            );

            if (nonCommentLines.length < 3) {
                // File is mostly empty/comments, replace with valid content
                const validCargoContent = `[package]
name = "actix-web-api"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
actix-cors = "0.6"
`;
                fs.writeFileSync(cargoTomlPath, validCargoContent);
                return true;
            }
        }

        return false;
    } catch (error) {
        console.error('Error fixing Cargo.toml syntax:', error);
        return false;
    }
}

// Fix missing package.json
async function fixMissingPackageJson(projectSrcPath) {
    try {
        const packageJsonPath = path.join(projectSrcPath, 'package.json');

        const packageContent = {
            "name": "react-app",
            "version": "0.1.0",
            "private": true,
            "dependencies": {
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-scripts": "5.0.1"
            },
            "scripts": {
                "start": "react-scripts start",
                "build": "react-scripts build",
                "test": "react-scripts test",
                "eject": "react-scripts eject"
            }
        };

        fs.writeFileSync(packageJsonPath, JSON.stringify(packageContent, null, 2));
        return true;
    } catch (error) {
        console.error('Error creating package.json:', error);
        return false;
    }
}

// Fix missing npm dependencies
async function fixMissingNpmDependencies(projectSrcPath) {
    try {
        // Run npm install to fix missing dependencies
        return new Promise((resolve) => {
            const npmInstall = spawn('npm', ['install'], {
                cwd: projectSrcPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            npmInstall.on('close', (code) => {
                resolve(code === 0);
            });
        });
    } catch (error) {
        console.error('Error running npm install:', error);
        return false;
    }
}

// Build and run project
app.post('/api/projects/:id/build-and-run', async (req, res) => {
    const { id: projectId } = req.params;

    try {
        // Get project details
        const project = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM projects WHERE id = ?', [projectId], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        const workspacePath = path.join(__dirname, '..', 'workspaces', projectId);
        const commands = getBuildCommands(project.tech_stack);
        const projectSrcPath = path.join(workspacePath, commands.workingDir);

        // Check if project source exists
        if (!fs.existsSync(projectSrcPath)) {
            return res.status(400).json({
                success: false,
                output: `❌ Project source not found at ${projectSrcPath}\nPlease ensure the AI has generated the project code first.`
            });
        }

        let output = `🔨 Building ${project.name} (${project.tech_stack})...\n`;
        output += `📁 Working directory: ${projectSrcPath}\n`;
        output += `⚡ Running: ${commands.build}\n\n`;

        // Stop any existing process for this project
        if (runningProcesses.has(projectId)) {
            const existingProcess = runningProcesses.get(projectId);
            existingProcess.kill();
            runningProcesses.delete(projectId);
            output += '🛑 Stopped existing process\n';
        }

        // Build the project
        const buildProcess = spawn(commands.build.split(' ')[0], commands.build.split(' ').slice(1), {
            cwd: projectSrcPath,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let buildOutput = '';
        let buildError = '';

        buildProcess.stdout.on('data', (data) => {
            buildOutput += data.toString();
        });

        buildProcess.stderr.on('data', (data) => {
            buildError += data.toString();
        });

        buildProcess.on('close', async (code) => {
            if (code === 0) {
                output += '✅ Build successful!\n';
                output += buildOutput;
                output += `\n🚀 Starting application: ${commands.run}\n`;

                // Run the application
                const runProcess = spawn(commands.run.split(' ')[0], commands.run.split(' ').slice(1), {
                    cwd: projectSrcPath,
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                // Store the running process with metadata
                runningProcesses.set(projectId, {
                    process: runProcess,
                    port: commands.defaultPort,
                    techStack: project.tech_stack,
                    startTime: new Date()
                });

                let appOutput = '';
                let detectedPort = commands.defaultPort;

                runProcess.stdout.on('data', (data) => {
                    const dataStr = data.toString();
                    appOutput += dataStr;
                    console.log(`[${projectId}] ${dataStr}`);

                    // Try to detect the actual port
                    const port = detectApplicationPort(dataStr, commands.defaultPort);
                    if (port !== commands.defaultPort) {
                        detectedPort = port;
                        // Update stored process info
                        const processInfo = runningProcesses.get(projectId);
                        if (processInfo) {
                            processInfo.port = port;
                        }
                    }
                });

                runProcess.stderr.on('data', (data) => {
                    const dataStr = data.toString();
                    appOutput += dataStr;
                    console.error(`[${projectId}] ${dataStr}`);
                });

                runProcess.on('close', (runCode) => {
                    console.log(`[${projectId}] Process exited with code ${runCode}`);
                    runningProcesses.delete(projectId);
                });

                // Wait a moment for the application to start and detect port
                setTimeout(() => {
                    const finalPort = detectApplicationPort(appOutput, commands.defaultPort);
                    const appUrl = `http://localhost:${finalPort}${commands.urlPath}`;

                    output += '✅ Application started successfully!\n';
                    output += `🌐 Application URL: ${appUrl}\n`;
                    output += `📡 Check the console for runtime output\n`;

                    res.json({
                        success: true,
                        output,
                        appUrl: appUrl,
                        port: finalPort
                    });
                }, 2000);

                // Don't send response immediately, wait for setTimeout above
            } else {
                output += '❌ Build failed!\n';
                output += buildOutput;
                output += buildError;

                // Attempt AI-powered build fix
                try {
                    output += '\n🤖 AI attempting to fix build issues...\n';
                    const fixResult = await attemptBuildFix(projectId, project.tech_stack, buildError, projectSrcPath);

                    if (fixResult.fixed) {
                        output += fixResult.message + '\n';
                        output += '🔄 Retrying build...\n';

                        // Retry the build
                        const retryProcess = spawn(commands.build.split(' ')[0], commands.build.split(' ').slice(1), {
                            cwd: projectSrcPath,
                            stdio: ['pipe', 'pipe', 'pipe']
                        });

                        let retryOutput = '';
                        let retryError = '';

                        retryProcess.stdout.on('data', (data) => {
                            retryOutput += data.toString();
                        });

                        retryProcess.stderr.on('data', (data) => {
                            retryError += data.toString();
                        });

                        retryProcess.on('close', (retryCode) => {
                            if (retryCode === 0) {
                                output += '✅ Build fixed and successful!\n';
                                output += retryOutput;
                                output += `\n🚀 Starting application: ${commands.run}\n`;

                                // Start the application after successful fix
                                const runProcess = spawn(commands.run.split(' ')[0], commands.run.split(' ').slice(1), {
                                    cwd: projectSrcPath,
                                    stdio: ['pipe', 'pipe', 'pipe']
                                });

                                // Store the running process with metadata
                                runningProcesses.set(projectId, {
                                    process: runProcess,
                                    port: commands.defaultPort,
                                    techStack: project.tech_stack,
                                    startTime: new Date()
                                });

                                let appOutput = '';

                                runProcess.stdout.on('data', (data) => {
                                    const dataStr = data.toString();
                                    appOutput += dataStr;
                                    console.log(`[${projectId}] ${dataStr}`);
                                });

                                runProcess.stderr.on('data', (data) => {
                                    const dataStr = data.toString();
                                    appOutput += dataStr;
                                    console.error(`[${projectId}] ${dataStr}`);
                                });

                                runProcess.on('close', (runCode) => {
                                    console.log(`[${projectId}] Process exited with code ${runCode}`);
                                    runningProcesses.delete(projectId);
                                });

                                // Wait for app to start and detect port
                                setTimeout(() => {
                                    const finalPort = detectApplicationPort(appOutput, commands.defaultPort);
                                    const appUrl = `http://localhost:${finalPort}${commands.urlPath}`;

                                    output += '✅ Application started successfully!\n';
                                    output += `🌐 Application URL: ${appUrl}\n`;
                                    output += `📡 Check the console for runtime output\n`;

                                    res.json({
                                        success: true,
                                        output,
                                        fixed: true,
                                        appUrl: appUrl,
                                        port: finalPort
                                    });
                                }, 2000);
                            } else {
                                output += '❌ Build still failed after fix attempt\n';
                                output += retryError;
                                res.json({ success: false, output });
                            }
                        });
                    } else {
                        output += fixResult.message + '\n';
                        res.json({ success: false, output });
                    }
                } catch (fixError) {
                    output += `❌ AI fix attempt failed: ${fixError.message}\n`;
                    res.json({ success: false, output });
                }
            }
        });

    } catch (error) {
        console.error('Build and run error:', error);
        res.status(500).json({
            success: false,
            output: `❌ Error: ${error.message}`
        });
    }
});

// Stop running project
app.post('/api/projects/:id/stop', (req, res) => {
    const { id: projectId } = req.params;

    if (runningProcesses.has(projectId)) {
        const processInfo = runningProcesses.get(projectId);
        const process = processInfo.process || processInfo; // Handle both old and new format
        process.kill();
        runningProcesses.delete(projectId);
        res.json({ success: true, message: 'Application stopped' });
    } else {
        res.json({ success: false, message: 'No running process found' });
    }
});

// Get running application info
app.get('/api/projects/:id/app-info', (req, res) => {
    const { id: projectId } = req.params;

    if (runningProcesses.has(projectId)) {
        const processInfo = runningProcesses.get(projectId);
        res.json({
            running: true,
            port: processInfo.port,
            techStack: processInfo.techStack,
            startTime: processInfo.startTime,
            url: `http://localhost:${processInfo.port}`
        });
    } else {
        res.json({ running: false });
    }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, async () => {
    console.log(`Server is running on port ${PORT}`);

    // Auto-start AI scheduler with hourly validation configuration
    try {
        const hourlyConfig = {
            tickRate: 3600000, // 1 hour
            guardrails: {
                enableAcceptanceCriteriaValidation: true,
                validationScoreThreshold: 70,
                maxStoriesPerTick: 5,
                maxConcurrentStoriesPerProject: 5
            }
        };

        const started = await aiScheduler.start(hourlyConfig);
        if (started) {
            console.log('🤖 AI Scheduler started automatically with hourly validation enabled');

            // Try to save state to database (non-blocking)
            try {
                const query = `INSERT OR REPLACE INTO ai_scheduler_state (
                    id, is_running, tick_rate, config, started_at, updated_at
                ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;

                db.run(query, [
                    'singleton',
                    true,
                    aiScheduler.config.tickRate,
                    JSON.stringify(aiScheduler.config)
                ], (err) => {
                    if (err) {
                        console.warn('⚠️ Could not save scheduler state to database:', err.message);
                    } else {
                        console.log('💾 Scheduler state saved to database');
                    }
                });
            } catch (dbError) {
                console.warn('⚠️ Database state save failed:', dbError.message);
            }
        }
    } catch (error) {
        console.error('⚠️ Failed to auto-start AI scheduler:', error.message);
    }
});
