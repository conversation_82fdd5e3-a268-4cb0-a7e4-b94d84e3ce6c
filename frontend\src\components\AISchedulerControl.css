.ai-scheduler-control {
  flex: 1;
  background: #1a1a1a;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
}

.ai-scheduler-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #9ca3af;
}

.scheduler-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.scheduler-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
}

.scheduler-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: 500;
  font-size: 14px;
  color: #e5e5e5;
}

.scheduler-content {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-section {
  display: flex;
  justify-content: flex-start;
}

.stat-card {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
  max-width: 400px;
}

.stat-card h3 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.stat-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.stat-label {
  font-weight: 400;
  color: #9ca3af;
  font-size: 13px;
}

.stat-value {
  font-weight: 600;
  color: #e5e5e5;
  font-size: 13px;
}

/* Horizontal Sections */
.horizontal-section {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
}

.horizontal-section h3 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.horizontal-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.horizontal-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 6px;
  transition: border-color 0.15s ease;
}

.horizontal-item:hover {
  border-color: #4b5563;
}

.item-name {
  font-weight: 500;
  color: #e5e5e5;
  font-size: 14px;
  min-width: 120px;
}

.item-status {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

.item-status.enabled {
  background-color: #065f46;
  color: #10b981;
}

.item-status.disabled {
  background-color: #7f1d1d;
  color: #f87171;
}

.item-project {
  color: #9ca3af;
  font-size: 13px;
  font-style: italic;
}

.item-dates {
  color: #9ca3af;
  font-size: 12px;
  margin-left: auto;
}

.empty-state {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 20px;
}



.scheduler-controls {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin: 0 24px 24px 24px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-success {
  background-color: #059669;
  color: white;
  border-color: #059669;
}

.btn-success:hover:not(:disabled) {
  background-color: #047857;
  border-color: #047857;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  border-color: #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.config-section {
  border-top: 1px solid #374151;
  padding-top: 20px;
}

.config-section h3 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.config-group {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.config-group label {
  min-width: 180px;
  font-weight: 400;
  color: #e5e5e5;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.config-group input[type="number"],
.config-group select {
  padding: 6px 10px;
  border: 1px solid #374151;
  border-radius: 4px;
  font-size: 13px;
  min-width: 100px;
  background: #1a1a1a;
  color: #e5e5e5;
}

.config-group input[type="number"]:focus,
.config-group select:focus {
  outline: none;
  border-color: #4b5563;
}

.config-group input[type="number"]:disabled,
.config-group select:disabled {
  background-color: #374151;
  color: #9ca3af;
  opacity: 0.6;
}

.config-group input[type="checkbox"] {
  margin: 0;
  transform: scale(1.1);
  accent-color: #2563eb;
}

.config-group input[type="checkbox"]:disabled {
  opacity: 0.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .scheduler-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
  }

  .scheduler-stats {
    grid-template-columns: 1fr;
    padding: 16px 20px;
  }

  .stat-grid {
    grid-template-columns: 1fr;
  }

  .scheduler-controls {
    margin: 0 20px 20px 20px;
    padding: 16px;
  }

  .control-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .config-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .config-group label {
    min-width: auto;
  }
}
