import React, { useState } from 'react';
import {
  FileText,
  Calendar,
  BarChart3,
  Settings,
  Users,
  Target,
  GitBranch,
  Clock,
  CheckCircle,
  Bot,
  ArrowLeft,
  Play,
  Square,
  Loader
} from 'lucide-react';
import './ProjectSidebar.css';

const ProjectSidebar = ({ activeView, onViewChange, project, stats, onBack }) => {
  const [buildStatus, setBuildStatus] = useState('idle'); // idle, building, running, stopped
  const [buildOutput, setBuildOutput] = useState('');
  const [appUrl, setAppUrl] = useState('');
  const [appPort, setAppPort] = useState(null);

  const handleBuildAndRun = async () => {
    if (buildStatus === 'running') {
      // Stop the application
      setBuildStatus('building');
      try {
        const response = await fetch(`/api/projects/${project.id}/stop`, {
          method: 'POST'
        });
        if (response.ok) {
          setBuildStatus('stopped');
          setBuildOutput(prev => prev + '\n🛑 Application stopped');
        }
      } catch (error) {
        console.error('Error stopping application:', error);
        setBuildStatus('idle');
      }
      return;
    }

    // Clear output and start building
    setBuildStatus('building');
    setBuildOutput('🔨 Building application...\n');
    setAppUrl('');
    setAppPort(null);

    try {
      const response = await fetch(`/api/projects/${project.id}/build-and-run`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Build failed: ${response.statusText}`);
      }

      const result = await response.json();
      setBuildOutput(prev => prev + result.output + '\n');

      if (result.success) {
        setBuildStatus('running');
        if (result.appUrl) {
          setAppUrl(result.appUrl);
          setAppPort(result.port);
          setBuildOutput(prev => prev + `✅ Application is running!\n🌐 Access at: ${result.appUrl}\n`);
        } else {
          setBuildOutput(prev => prev + '✅ Application is running!\n');
        }
      } else {
        setBuildStatus('idle');
        setBuildOutput(prev => prev + '❌ Build failed\n');
      }
    } catch (error) {
      console.error('Error building application:', error);
      setBuildStatus('idle');
      setBuildOutput(prev => prev + `❌ Error: ${error.message}\n`);
    }
  };

  const getBuildButtonText = () => {
    switch (buildStatus) {
      case 'building': return 'Building...';
      case 'running': return 'Stop App';
      case 'stopped': return 'Build & Run';
      default: return 'Build & Run';
    }
  };

  const getBuildButtonIcon = () => {
    switch (buildStatus) {
      case 'building': return Loader;
      case 'running': return Square;
      default: return Play;
    }
  };

  const menuItems = [
    {
      id: 'stories',
      label: 'Stories',
      icon: FileText,
      count: stats?.total || 0
    },
    {
      id: 'sprints',
      label: 'Sprints',
      icon: Calendar,
      count: null
    },
    {
      id: 'board',
      label: 'Board',
      icon: Target,
      count: null
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: BarChart3,
      count: null
    },
    {
      id: 'ai-scheduler',
      label: 'AI Scheduler',
      icon: Bot,
      count: null
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      count: null
    }
  ];

  const statusItems = [
    {
      label: 'Backlog',
      count: stats?.backlog || 0,
      color: '#6b7280'
    },
    {
      label: 'In Progress',
      count: stats?.inProgress || 0,
      color: '#3b82f6'
    },
    {
      label: 'Completed',
      count: stats?.completed || 0,
      color: '#10b981'
    }
  ];

  return (
    <div className="project-sidebar">
      {/* Project Header */}
      <div className="sidebar-header">
        <button className="back-button" onClick={onBack} title="Back to Dashboard">
          <ArrowLeft size={16} />
          <span>Back to Dashboard</span>
        </button>
        <div className="project-info">
          <div className="project-avatar">
            {project?.name?.charAt(0) || 'P'}
          </div>
          <div className="project-details">
            <h3 className="project-name">{project?.name || 'Project'}</h3>
            <span className="project-status">{project?.status || 'Active'}</span>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <div className="nav-section">
          <div className="nav-section-header">
            <span>Project</span>
          </div>
          <ul className="nav-list">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <li key={item.id}>
                  <button
                    className={`nav-item ${activeView === item.id ? 'active' : ''}`}
                    onClick={() => onViewChange(item.id)}
                  >
                    <Icon className="nav-icon" size={16} />
                    <span className="nav-label">{item.label}</span>
                    {item.count !== null && (
                      <span className="nav-count">{item.count}</span>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>

        {/* Status Overview */}
        <div className="nav-section">
          <div className="nav-section-header">
            <span>Status</span>
          </div>
          <ul className="status-list">
            {statusItems.map((status, index) => (
              <li key={index} className="status-item">
                <div 
                  className="status-indicator"
                  style={{ backgroundColor: status.color }}
                />
                <span className="status-label">{status.label}</span>
                <span className="status-count">{status.count}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Quick Actions */}
        <div className="nav-section">
          <div className="nav-section-header">
            <span>Quick Actions</span>
          </div>
          <div className="quick-actions">
            <button className="quick-action-btn">
              <FileText size={14} />
              New Story
            </button>
            <button className="quick-action-btn">
              <Calendar size={14} />
              New Sprint
            </button>
            <button
              className={`quick-action-btn build-run-btn ${buildStatus}`}
              onClick={handleBuildAndRun}
              disabled={buildStatus === 'building'}
              title={buildStatus === 'running' ? 'Stop the running application' : 'Build and run the application'}
            >
              {React.createElement(getBuildButtonIcon(), {
                size: 14,
                className: buildStatus === 'building' ? 'spinning' : ''
              })}
              {getBuildButtonText()}
            </button>
          </div>
        </div>
      </nav>

      {/* Project Stats */}
      <div className="sidebar-footer">
        <div className="project-stats">
          <div className="stat-item">
            <Clock size={14} />
            <span>Active Sprint</span>
          </div>
          <div className="stat-item">
            <GitBranch size={14} />
            <span>v1.0.0</span>
          </div>
          <div className="stat-item">
            <Users size={14} />
            <span>AI Team</span>
          </div>
        </div>
      </div>

      {/* Application Access - positioned above build output when running */}
      {buildStatus === 'running' && appUrl && (
        <div className="app-access">
          <div className="app-access-header">
            <span>🌐 Application Running</span>
          </div>
          <div className="app-access-content">
            <div className="app-url-section">
              <span className="app-url-label">Access URL:</span>
              <a
                href={appUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="app-url-link"
                title="Open application in new tab"
              >
                {appUrl}
              </a>
            </div>
            <div className="app-info">
              <span className="app-port">Port: {appPort}</span>
              <span className="app-status">● Running</span>
            </div>
          </div>
        </div>
      )}

      {/* Build Output - positioned at bottom */}
      {buildOutput && (
        <div className="build-output">
          <div className="build-output-header">
            <span>Build Output</span>
            <button
              className="clear-output-btn"
              onClick={() => setBuildOutput('')}
              title="Clear output"
            >
              ×
            </button>
          </div>
          <pre className="build-output-content">{buildOutput}</pre>
        </div>
      )}
    </div>
  );
};

export default ProjectSidebar;
